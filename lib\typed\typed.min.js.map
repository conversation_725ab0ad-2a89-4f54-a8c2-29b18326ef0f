{"version": 3, "sources": ["typed.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "Object", "defineProperty", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "protoProps", "staticProps", "prototype", "_initializerJs", "_htmlParserJs", "Typed", "elementId", "options", "initializer", "load", "begin", "pause", "status", "start", "stop", "typingComplete", "toggleBlinking", "onStop", "arrayPos", "typewrite", "curString", "curStr<PERSON>os", "backspace", "onStart", "reset", "onDestroy", "restart", "arguments", "undefined", "clearInterval", "timeout", "replaceText", "cursor", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "strPos", "curL<PERSON>", "insertCursor", "onReset", "_this", "shuffleStringsIfNeeded", "bindInputFocusEvents", "bindFocusEvents", "setTimeout", "currentElContent", "strings", "sequence", "startDelay", "_this2", "fadeOut", "el", "classList", "contains", "fadeOutClass", "remove", "humanize", "humanizer", "typeSpeed", "numChars", "setPauseStatus", "htmlParser", "typeHtmlChars", "pauseTime", "substr", "char<PERSON>t", "test", "skip", "exec", "parseInt", "temporaryPause", "onTypingPaused", "substring", "stringBeforeSkip", "stringSkipped", "stringAfterSkip", "doneTyping", "keepTyping", "onTypingResumed", "preStringTyped", "nextString", "_this3", "onStringTyped", "complete", "loop", "loopCount", "<PERSON><PERSON><PERSON><PERSON>", "_this4", "initFadeOut", "backSpeed", "backSpaceHtmlChars", "curStringAtPosition", "smartBackspace", "stopNum", "onLastStringBackspaced", "onComplete", "isTyping", "isBlinking", "cursorBlinking", "style", "animationIterationCount", "speed", "Math", "round", "random", "shuffle", "sort", "_this5", "className", "fadeOutDelay", "str", "attr", "setAttribute", "isInput", "contentType", "innerHTML", "textContent", "_this6", "addEventListener", "e", "showCursor", "document", "createElement", "cursor<PERSON><PERSON>", "insertBefore", "nextS<PERSON>ling", "_interopRequireDefault", "obj", "__esModule", "default", "_extends", "assign", "source", "hasOwnProperty", "_defaultsJs", "_defaultsJs2", "Initializer", "self", "querySelector", "tagName", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "getAttribute", "isPaused", "map", "s", "trim", "stringsElement", "display", "Array", "slice", "apply", "children", "<PERSON><PERSON><PERSON><PERSON>", "stringEl", "push", "getCurrentElContent", "autoInsertCss", "appendAnimationCss", "css", "type", "innerCss", "head", "append<PERSON><PERSON><PERSON>", "defaults", "Infinity", "HTMLParser", "cur<PERSON><PERSON>", "endTag"], "mappings": ";;;;;;;;;CASA,SAA2CA,EAAMC,GAC1B,gBAAZC,UAA0C,gBAAXC,QACxCA,OAAOD,QAAUD,IACQ,kBAAXG,SAAyBA,OAAOC,IAC9CD,UAAWH,GACe,gBAAZC,SACdA,QAAe,MAAID,IAEnBD,EAAY,MAAIC,MACfK,KAAM,WACT,MAAgB,UAAUC,GAKhB,QAASC,GAAoBC,GAG5B,GAAGC,EAAiBD,GACnB,MAAOC,GAAiBD,GAAUP,OAGnC,IAAIC,GAASO,EAAiBD,IAC7BP,WACAS,GAAIF,EACJG,QAAQ,EAUT,OANAL,GAAQE,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOS,QAAS,EAGTT,EAAOD,QAvBf,GAAIQ,KAqCJ,OATAF,GAAoBM,EAAIP,EAGxBC,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,GAGjBR,EAAoB,KAK/B,SAAUL,EAAQD,EAASM,GAEhC,YAQA,SAASS,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCANhHC,OAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAGT,IAAIC,GAAe,WAAe,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CAAE,GAAIE,GAAaH,EAAMC,EAAIE,GAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,SAAWF,KAAYA,EAAWG,UAAW,GAAMZ,OAAOC,eAAeI,EAAQI,EAAWI,IAAKJ,IAAiB,MAAO,UAAUX,EAAagB,EAAYC,GAAiJ,MAA9HD,IAAYV,EAAiBN,EAAYkB,UAAWF,GAAiBC,GAAaX,EAAiBN,EAAaiB,GAAqBjB,MAI7hBmB,EAAiB9B,EAAoB,GAErC+B,EAAgB/B,EAAoB,GASpCgC,EAAQ,WACV,QAASA,GAAMC,EAAWC,GACxBzB,EAAgBX,KAAMkC,GAGtBF,EAAeK,YAAYC,KAAKtC,KAAMoC,EAASD,GAE/CnC,KAAKuC,QA6cP,MArcArB,GAAagB,IACXN,IAAK,SACLX,MAAO,WACLjB,KAAKwC,MAAMC,OAASzC,KAAK0C,QAAU1C,KAAK2C,UAQ1Cf,IAAK,OACLX,MAAO,WACDjB,KAAK4C,gBACL5C,KAAKwC,MAAMC,SACfzC,KAAK6C,gBAAe,GACpB7C,KAAKwC,MAAMC,QAAS,EACpBzC,KAAKoC,QAAQU,OAAO9C,KAAK+C,SAAU/C,UAQrC4B,IAAK,QACLX,MAAO,WACDjB,KAAK4C,gBACJ5C,KAAKwC,MAAMC,SAChBzC,KAAKwC,MAAMC,QAAS,EAChBzC,KAAKwC,MAAMQ,UACbhD,KAAKgD,UAAUhD,KAAKwC,MAAMS,UAAWjD,KAAKwC,MAAMU,WAEhDlD,KAAKmD,UAAUnD,KAAKwC,MAAMS,UAAWjD,KAAKwC,MAAMU,WAElDlD,KAAKoC,QAAQgB,QAAQpD,KAAK+C,SAAU/C,UAQtC4B,IAAK,UACLX,MAAO,WACLjB,KAAKqD,OAAM,GACXrD,KAAKoC,QAAQkB,UAAUtD,SASzB4B,IAAK,QACLX,MAAO,WACL,GAAIsC,GAAUC,UAAUjC,QAAU,GAAsBkC,SAAjBD,UAAU,IAA0BA,UAAU,EAErFE,eAAc1D,KAAK2D,SACnB3D,KAAK4D,YAAY,IACb5D,KAAK6D,QAAU7D,KAAK6D,OAAOC,aAC7B9D,KAAK6D,OAAOC,WAAWC,YAAY/D,KAAK6D,QACxC7D,KAAK6D,OAAS,MAEhB7D,KAAKgE,OAAS,EACdhE,KAAK+C,SAAW,EAChB/C,KAAKiE,QAAU,EACXV,IACFvD,KAAKkE,eACLlE,KAAKoC,QAAQ+B,QAAQnE,MACrBA,KAAKuC,YASTX,IAAK,QACLX,MAAO,WACL,GAAImD,GAAQpE,IAEZA,MAAK4C,gBAAiB,EACtB5C,KAAKqE,uBAAuBrE,MAC5BA,KAAKkE,eACDlE,KAAKsE,sBAAsBtE,KAAKuE,kBACpCvE,KAAK2D,QAAUa,WAAW,WAEnBJ,EAAMK,kBAAsD,IAAlCL,EAAMK,iBAAiBlD,OAIpD6C,EAAMjB,UAAUiB,EAAMK,iBAAkBL,EAAMK,iBAAiBlD,QAH/D6C,EAAMpB,UAAUoB,EAAMM,QAAQN,EAAMO,SAASP,EAAMrB,WAAYqB,EAAMJ,SAKtEhE,KAAK4E,eAUVhD,IAAK,YACLX,MAAO,SAAmBgC,EAAWC,GACnC,GAAI2B,GAAS7E,IAETA,MAAK8E,SAAW9E,KAAK+E,GAAGC,UAAUC,SAASjF,KAAKkF,gBAClDlF,KAAK+E,GAAGC,UAAUG,OAAOnF,KAAKkF,cAC1BlF,KAAK6D,QAAQ7D,KAAK6D,OAAOmB,UAAUG,OAAOnF,KAAKkF,cAGrD,IAAIE,GAAWpF,KAAKqF,UAAUrF,KAAKsF,WAC/BC,EAAW,CAEf,OAAIvF,MAAKwC,MAAMC,UAAW,MACxBzC,MAAKwF,eAAevC,EAAWC,GAAW,QAK5ClD,KAAK2D,QAAUa,WAAW,WAExBtB,EAAYjB,EAAcwD,WAAWC,cAAczC,EAAWC,EAAW2B,EAEzE,IAAIc,GAAY,EACZC,EAAS3C,EAAU2C,OAAO1C,EAI9B,IAAyB,MAArB0C,EAAOC,OAAO,IACZ,SAASC,KAAKF,GAAS,CACzB,GAAIG,GAAO,CACXH,GAAS,MAAMI,KAAKJ,GAAQ,GAC5BG,GAAQH,EAAOrE,OACfoE,EAAYM,SAASL,GACrBf,EAAOqB,gBAAiB,EACxBrB,EAAOzC,QAAQ+D,eAAetB,EAAO9B,SAAU8B,GAE/C5B,EAAYA,EAAUmD,UAAU,EAAGlD,GAAaD,EAAUmD,UAAUlD,EAAY6C,GAChFlB,EAAOhC,gBAAe,GAM1B,GAAyB,MAArB+C,EAAOC,OAAO,GAAY,CAC5B,KAA4D,MAArD5C,EAAU2C,OAAO1C,EAAYqC,GAAUM,OAAO,KACnDN,MACIrC,EAAYqC,EAAWtC,EAAU1B,WAGvC,GAAI8E,GAAmBpD,EAAUmD,UAAU,EAAGlD,GAC1CoD,EAAgBrD,EAAUmD,UAAUC,EAAiB9E,OAAS,EAAG2B,EAAYqC,GAC7EgB,EAAkBtD,EAAUmD,UAAUlD,EAAYqC,EAAW,EACjEtC,GAAYoD,EAAmBC,EAAgBC,EAC/ChB,IAIFV,EAAOlB,QAAUa,WAAW,WAE1BK,EAAOhC,gBAAe,GAGlBK,IAAcD,EAAU1B,OAC1BsD,EAAO2B,WAAWvD,EAAWC,GAE7B2B,EAAO4B,WAAWxD,EAAWC,EAAWqC,GAGtCV,EAAOqB,iBACTrB,EAAOqB,gBAAiB,EACxBrB,EAAOzC,QAAQsE,gBAAgB7B,EAAO9B,SAAU8B,KAEjDc,IAGFP,OAULxD,IAAK,aACLX,MAAO,SAAoBgC,EAAWC,EAAWqC,GAE7B,IAAdrC,IACFlD,KAAK6C,gBAAe,GACpB7C,KAAKoC,QAAQuE,eAAe3G,KAAK+C,SAAU/C,OAI7CkD,GAAaqC,CACb,IAAIqB,GAAa3D,EAAU2C,OAAO,EAAG1C,EACrClD,MAAK4D,YAAYgD,GAEjB5G,KAAKgD,UAAUC,EAAWC,MAU5BtB,IAAK,aACLX,MAAO,SAAoBgC,EAAWC,GACpC,GAAI2D,GAAS7G,IAGbA,MAAKoC,QAAQ0E,cAAc9G,KAAK+C,SAAU/C,MAC1CA,KAAK6C,gBAAe,GAEhB7C,KAAK+C,WAAa/C,KAAK0E,QAAQnD,OAAS,IAE1CvB,KAAK+G,WAED/G,KAAKgH,QAAS,GAAShH,KAAKiE,UAAYjE,KAAKiH,aAInDjH,KAAK2D,QAAUa,WAAW,WACxBqC,EAAO1D,UAAUF,EAAWC,IAC3BlD,KAAKkH,eAUVtF,IAAK,YACLX,MAAO,SAAmBgC,EAAWC,GACnC,GAAIiE,GAASnH,IAEb,IAAIA,KAAKwC,MAAMC,UAAW,EAExB,WADAzC,MAAKwF,eAAevC,EAAWC,GAAW,EAG5C,IAAIlD,KAAK8E,QAAS,MAAO9E,MAAKoH,aAE9BpH,MAAK6C,gBAAe,EACpB,IAAIuC,GAAWpF,KAAKqF,UAAUrF,KAAKqH,UAEnCrH,MAAK2D,QAAUa,WAAW,WACxBtB,EAAYjB,EAAcwD,WAAW6B,mBAAmBrE,EAAWC,EAAWiE,EAE9E,IAAII,GAAsBtE,EAAU2C,OAAO,EAAG1C,EAI9C,IAHAiE,EAAOvD,YAAY2D,GAGfJ,EAAOK,eAAgB,CAEzB,GAAIZ,GAAaO,EAAOzC,QAAQyC,EAAOpE,SAAW,EAC9C6D,IAAcW,IAAwBX,EAAWhB,OAAO,EAAG1C,GAC7DiE,EAAOM,QAAUvE,EAEjBiE,EAAOM,QAAU,EAMjBvE,EAAYiE,EAAOM,SAErBvE,IAEAiE,EAAOhE,UAAUF,EAAWC,IACnBA,GAAaiE,EAAOM,UAG7BN,EAAOpE,WAEHoE,EAAOpE,WAAaoE,EAAOzC,QAAQnD,QACrC4F,EAAOpE,SAAW,EAClBoE,EAAO/E,QAAQsF,yBACfP,EAAO9C,yBACP8C,EAAO5E,SAEP4E,EAAOnE,UAAUmE,EAAOzC,QAAQyC,EAAOxC,SAASwC,EAAOpE,WAAYG,KAItEkC,MAQLxD,IAAK,WACLX,MAAO,WACLjB,KAAKoC,QAAQuF,WAAW3H,MACpBA,KAAKgH,KACPhH,KAAKiE,UAELjE,KAAK4C,gBAAiB,KAY1BhB,IAAK,iBACLX,MAAO,SAAwBgC,EAAWC,EAAW0E,GACnD5H,KAAKwC,MAAMQ,UAAY4E,EACvB5H,KAAKwC,MAAMS,UAAYA,EACvBjD,KAAKwC,MAAMU,UAAYA,KASzBtB,IAAK,iBACLX,MAAO,SAAwB4G,GAC7B,GAAK7H,KAAK6D,SAEN7D,KAAKwC,MAAMC,QACXzC,KAAK8H,iBAAmBD,EAA5B,CACA7H,KAAK8H,eAAiBD,CACtB,IAAIpF,GAASoF,EAAa,WAAa,CACvC7H,MAAK6D,OAAOkE,MAAMC,wBAA0BvF,MAS9Cb,IAAK,YACLX,MAAO,SAAmBgH,GACxB,MAAOC,MAAKC,MAAMD,KAAKE,SAAWH,EAAQ,GAAKA,KAQjDrG,IAAK,yBACLX,MAAO,WACAjB,KAAKqI,UACVrI,KAAK2E,SAAW3E,KAAK2E,SAAS2D,KAAK,WACjC,MAAOJ,MAAKE,SAAW,SAS3BxG,IAAK,cACLX,MAAO,WACL,GAAIsH,GAASvI,IAIb,OAFAA,MAAK+E,GAAGyD,WAAa,IAAMxI,KAAKkF,aAC5BlF,KAAK6D,SAAQ7D,KAAK6D,OAAO2E,WAAa,IAAMxI,KAAKkF,cAC9CV,WAAW,WAChB+D,EAAOxF,WACPwF,EAAO3E,YAAY,IAGf2E,EAAO7D,QAAQnD,OAASgH,EAAOxF,SACjCwF,EAAOvF,UAAUuF,EAAO7D,QAAQ6D,EAAO5D,SAAS4D,EAAOxF,WAAY,IAEnEwF,EAAOvF,UAAUuF,EAAO7D,QAAQ,GAAI,GACpC6D,EAAOxF,SAAW,IAEnB/C,KAAKyI,iBAUV7G,IAAK,cACLX,MAAO,SAAqByH,GACtB1I,KAAK2I,KACP3I,KAAK+E,GAAG6D,aAAa5I,KAAK2I,KAAMD,GAE5B1I,KAAK6I,QACP7I,KAAK+E,GAAG9D,MAAQyH,EACc,SAArB1I,KAAK8I,YACd9I,KAAK+E,GAAGgE,UAAYL,EAEpB1I,KAAK+E,GAAGiE,YAAcN,KAW5B9G,IAAK,kBACLX,MAAO,WACL,GAAIgI,GAASjJ,IAERA,MAAK6I,UACV7I,KAAK+E,GAAGmE,iBAAiB,QAAS,SAAUC,GAC1CF,EAAOtG,SAET3C,KAAK+E,GAAGmE,iBAAiB,OAAQ,SAAUC,GACrCF,EAAOlE,GAAG9D,OAAoC,IAA3BgI,EAAOlE,GAAG9D,MAAMM,QAGvC0H,EAAOvG,cASXd,IAAK,eACLX,MAAO,WACAjB,KAAKoJ,aACNpJ,KAAK6D,SACT7D,KAAK6D,OAASwF,SAASC,cAAc,QACrCtJ,KAAK6D,OAAO2E,UAAY,eACxBxI,KAAK6D,OAAOkF,UAAY/I,KAAKuJ,WAC7BvJ,KAAK+E,GAAGjB,YAAc9D,KAAK+E,GAAGjB,WAAW0F,aAAaxJ,KAAK6D,OAAQ7D,KAAK+E,GAAG0E,mBAIxEvH,IAGTtC,GAAQ,WAAasC,EACrBrC,EAAOD,QAAUA,EAAQ,YAIpB,SAAUC,EAAQD,EAASM,GAEhC,YAUA,SAASwJ,GAAuBC,GAAO,MAAOA,IAAOA,EAAIC,WAAaD,GAAQE,UAAWF,GAEzF,QAAShJ,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAVhHC,OAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAGT,IAAI6I,GAAW/I,OAAOgJ,QAAU,SAAU3I,GAAU,IAAK,GAAIE,GAAI,EAAGA,EAAIkC,UAAUjC,OAAQD,IAAK,CAAE,GAAI0I,GAASxG,UAAUlC,EAAI,KAAK,GAAIM,KAAOoI,GAAcjJ,OAAOgB,UAAUkI,eAAe1J,KAAKyJ,EAAQpI,KAAQR,EAAOQ,GAAOoI,EAAOpI,IAAY,MAAOR,IAEnPF,EAAe,WAAe,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CAAE,GAAIE,GAAaH,EAAMC,EAAIE,GAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,SAAWF,KAAYA,EAAWG,UAAW,GAAMZ,OAAOC,eAAeI,EAAQI,EAAWI,IAAKJ,IAAiB,MAAO,UAAUX,EAAagB,EAAYC,GAAiJ,MAA9HD,IAAYV,EAAiBN,EAAYkB,UAAWF,GAAiBC,GAAaX,EAAiBN,EAAaiB,GAAqBjB,MAM7hBqJ,EAAchK,EAAoB,GAElCiK,EAAeT,EAAuBQ,GAMtCE,EAAc,WAChB,QAASA,KACPzJ,EAAgBX,KAAMoK,GAgLxB,MA7KAlJ,GAAakJ,IACXxI,IAAK,OAULX,MAAO,SAAcoJ,EAAMjI,EAASD,GAiElC,GA/DyB,gBAAdA,GACTkI,EAAKtF,GAAKsE,SAASiB,cAAcnI,GAEjCkI,EAAKtF,GAAK5C,EAGZkI,EAAKjI,QAAU0H,KAAaK,EAAa,WAAY/H,GAGrDiI,EAAKxB,QAA4C,UAAlCwB,EAAKtF,GAAGwF,QAAQC,cAC/BH,EAAK1B,KAAO0B,EAAKjI,QAAQuG,KACzB0B,EAAK/F,qBAAuB+F,EAAKjI,QAAQkC,qBAGzC+F,EAAKjB,YAAaiB,EAAKxB,SAAkBwB,EAAKjI,QAAQgH,WAGtDiB,EAAKd,WAAac,EAAKjI,QAAQmH,WAG/Bc,EAAKvC,gBAAiB,EAGtBuC,EAAKI,UAAYJ,EAAK1B,KAAO0B,EAAKtF,GAAG2F,aAAaL,EAAK1B,MAAQ0B,EAAKtF,GAAGiE,YAGvEqB,EAAKvB,YAAcuB,EAAKjI,QAAQ0G,YAGhCuB,EAAK/E,UAAY+E,EAAKjI,QAAQkD,UAG9B+E,EAAKzF,WAAayF,EAAKjI,QAAQwC,WAG/ByF,EAAKhD,UAAYgD,EAAKjI,QAAQiF,UAG9BgD,EAAK7C,eAAiB6C,EAAKjI,QAAQoF,eAGnC6C,EAAKnD,UAAYmD,EAAKjI,QAAQ8E,UAG9BmD,EAAKvF,QAAUuF,EAAKjI,QAAQ0C,QAC5BuF,EAAKnF,aAAemF,EAAKjI,QAAQ8C,aACjCmF,EAAK5B,aAAe4B,EAAKjI,QAAQqG,aAGjC4B,EAAKM,UAAW,EAGhBN,EAAK3F,QAAU2F,EAAKjI,QAAQsC,QAAQkG,IAAI,SAAUC,GAChD,MAAOA,GAAEC,SAIgC,gBAAhCT,GAAKjI,QAAQ2I,eACtBV,EAAKU,eAAiB1B,SAASiB,cAAcD,EAAKjI,QAAQ2I,gBAE1DV,EAAKU,eAAiBV,EAAKjI,QAAQ2I,eAGjCV,EAAKU,eAAgB,CACvBV,EAAK3F,WACL2F,EAAKU,eAAehD,MAAMiD,QAAU,MACpC,IAAItG,GAAUuG,MAAMlJ,UAAUmJ,MAAMC,MAAMd,EAAKU,eAAeK,UAC1DC,EAAgB3G,EAAQnD,MAE5B,IAAI8J,EACF,IAAK,GAAI/J,GAAI,EAAGA,EAAI+J,EAAe/J,GAAK,EAAG,CACzC,GAAIgK,GAAW5G,EAAQpD,EACvB+I,GAAK3F,QAAQ6G,KAAKD,EAASvC,UAAU+B,SAM3CT,EAAKrG,OAAS,EAGdqG,EAAKtH,SAAW,EAGhBsH,EAAK5C,QAAU,EAGf4C,EAAKrD,KAAOqD,EAAKjI,QAAQ4E,KACzBqD,EAAKpD,UAAYoD,EAAKjI,QAAQ6E,UAC9BoD,EAAKpG,QAAU,EAGfoG,EAAKhC,QAAUgC,EAAKjI,QAAQiG,QAE5BgC,EAAK1F,YAEL0F,EAAK7H,OACHC,QAAQ,EACRO,WAAW,EACXC,UAAW,GACXC,UAAW,GAIbmH,EAAKzH,gBAAiB,CAGtB,KAAK,GAAItB,KAAK+I,GAAK3F,QACjB2F,EAAK1F,SAASrD,GAAKA,CAIrB+I,GAAK5F,iBAAmBzE,KAAKwL,oBAAoBnB,GAEjDA,EAAKoB,cAAgBpB,EAAKjI,QAAQqJ,cAElCzL,KAAK0L,mBAAmBrB,MAG1BzI,IAAK,sBACLX,MAAO,SAA6BoJ,GAClC,GAAII,GAAY,EAUhB,OAREA,GADEJ,EAAK1B,KACK0B,EAAKtF,GAAG2F,aAAaL,EAAK1B,MAC7B0B,EAAKxB,QACFwB,EAAKtF,GAAG9D,MACU,SAArBoJ,EAAKvB,YACFuB,EAAKtF,GAAGgE,UAERsB,EAAKtF,GAAGiE,eAKxBpH,IAAK,qBACLX,MAAO,SAA4BoJ,GACjC,GAAKA,EAAKoB,eAGLpB,EAAKjB,YAAeiB,EAAKvF,QAA9B,CAIA,GAAI6G,GAAMtC,SAASC,cAAc,QACjCqC,GAAIC,KAAO,UACX,IAAIC,GAAW,EACXxB,GAAKjB,aACPyC,GAAY,8cAEVxB,EAAKvF,UACP+G,GAAY,+KAEK,IAAfF,EAAIpK,SAGRoK,EAAI5C,UAAY8C,EAChBxC,SAASyC,KAAKC,YAAYJ,SAIvBvB,IAGTxK,GAAQ,WAAawK,CACrB,IAAI/H,GAAc,GAAI+H,EACtBxK,GAAQyC,YAAcA,GAIjB,SAAUxC,EAAQD,GAQvB,YAEAmB,QAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAET,IAAI+K,IAKFtH,SAAU,kCAAmC,+BAAgC,gBAAiB,qBAC9FqG,eAAgB,KAKhBzF,UAAW,EAKXV,WAAY,EAKZyC,UAAW,EAKXG,gBAAgB,EAKhBa,SAAS,EAKTnB,UAAW,IAOXpC,SAAS,EACTI,aAAc,iBACduD,aAAc,IAMdzB,MAAM,EACNC,UAAWgF,EAAAA,EAOX7C,YAAY,EACZG,WAAY,IACZkC,eAAe,EAMf9C,KAAM,KAKNrE,sBAAsB,EAKtBwE,YAAa,OAMbnB,WAAY,SAAoB0C,KAOhC1D,eAAgB,SAAwB5D,EAAUsH,KAOlDvD,cAAe,SAAuB/D,EAAUsH,KAMhD3C,uBAAwB,SAAgC2C,KAOxDlE,eAAgB,SAAwBpD,EAAUsH,KAOlD3D,gBAAiB,SAAyB3D,EAAUsH,KAMpDlG,QAAS,SAAiBkG,KAO1BvH,OAAQ,SAAgBC,EAAUsH,KAOlCjH,QAAS,SAAiBL,EAAUsH,KAMpC/G,UAAW,SAAmB+G,KAGhCzK,GAAQ,WAAaoM,EACrBnM,EAAOD,QAAUA,EAAQ,YAIpB,SAAUC,EAAQD,GAQvB,YAQA,SAASe,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCANhHC,OAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAGT,IAAIC,GAAe,WAAe,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CAAE,GAAIE,GAAaH,EAAMC,EAAIE,GAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,SAAWF,KAAYA,EAAWG,UAAW,GAAMZ,OAAOC,eAAeI,EAAQI,EAAWI,IAAKJ,IAAiB,MAAO,UAAUX,EAAagB,EAAYC,GAAiJ,MAA9HD,IAAYV,EAAiBN,EAAYkB,UAAWF,GAAiBC,GAAaX,EAAiBN,EAAaiB,GAAqBjB,MAI7hBqL,EAAa,WACf,QAASA,KACPvL,EAAgBX,KAAMkM,GAoExB,MAjEAhL,GAAagL,IACXtK,IAAK,gBAWLX,MAAO,SAAuBgC,EAAWC,EAAWmH,GAClD,GAAyB,SAArBA,EAAKvB,YAAwB,MAAO5F,EACxC,IAAIiJ,GAAUlJ,EAAU2C,OAAO1C,GAAW2C,OAAO,EACjD,IAAgB,MAAZsG,GAA+B,MAAZA,EAAiB,CACtC,GAAIC,GAAS,EAMb,KAJEA,EADc,MAAZD,EACO,IAEA,IAEJlJ,EAAU2C,OAAO1C,EAAY,GAAG2C,OAAO,KAAOuG,IACnDlJ,MACIA,EAAY,EAAID,EAAU1B,WAIhC2B,IAEF,MAAOA,MAYTtB,IAAK,qBACLX,MAAO,SAA4BgC,EAAWC,EAAWmH,GACvD,GAAyB,SAArBA,EAAKvB,YAAwB,MAAO5F,EACxC,IAAIiJ,GAAUlJ,EAAU2C,OAAO1C,GAAW2C,OAAO,EACjD,IAAgB,MAAZsG,GAA+B,MAAZA,EAAiB,CACtC,GAAIC,GAAS,EAMb,KAJEA,EADc,MAAZD,EACO,IAEA,IAEJlJ,EAAU2C,OAAO1C,EAAY,GAAG2C,OAAO,KAAOuG,IACnDlJ,MACIA,EAAY,MAIlBA,IAEF,MAAOA,OAIJgJ,IAGTtM,GAAQ,WAAasM,CACrB,IAAIzG,GAAa,GAAIyG,EACrBtM,GAAQ6F,WAAaA", "file": "typed.min.js", "sourcesContent": ["/*!\n * \n *   typed.js - A JavaScript Typing Animation Library\n *   Author: <PERSON> <<EMAIL>>\n *   Version: v2.0.6\n *   Url: https://github.com/mattboldt/typed.js\n *   License(s): MIT\n * \n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Typed\"] = factory();\n\telse\n\t\troot[\"Typed\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\t\n\tvar _createClass = (function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; })();\n\t\n\tfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }\n\t\n\tvar _initializerJs = __webpack_require__(1);\n\t\n\tvar _htmlParserJs = __webpack_require__(3);\n\t\n\t/**\n\t * Welcome to Typed.js!\n\t * @param {string} elementId HTML element ID _OR_ HTML element\n\t * @param {object} options options object\n\t * @returns {object} a new Typed object\n\t */\n\t\n\tvar Typed = (function () {\n\t  function Typed(elementId, options) {\n\t    _classCallCheck(this, Typed);\n\t\n\t    // Initialize it up\n\t    _initializerJs.initializer.load(this, options, elementId);\n\t    // All systems go!\n\t    this.begin();\n\t  }\n\t\n\t  /**\n\t   * Toggle start() and stop() of the Typed instance\n\t   * @public\n\t   */\n\t\n\t  _createClass(Typed, [{\n\t    key: 'toggle',\n\t    value: function toggle() {\n\t      this.pause.status ? this.start() : this.stop();\n\t    }\n\t\n\t    /**\n\t     * Stop typing / backspacing and enable cursor blinking\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'stop',\n\t    value: function stop() {\n\t      if (this.typingComplete) return;\n\t      if (this.pause.status) return;\n\t      this.toggleBlinking(true);\n\t      this.pause.status = true;\n\t      this.options.onStop(this.arrayPos, this);\n\t    }\n\t\n\t    /**\n\t     * Start typing / backspacing after being stopped\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'start',\n\t    value: function start() {\n\t      if (this.typingComplete) return;\n\t      if (!this.pause.status) return;\n\t      this.pause.status = false;\n\t      if (this.pause.typewrite) {\n\t        this.typewrite(this.pause.curString, this.pause.curStrPos);\n\t      } else {\n\t        this.backspace(this.pause.curString, this.pause.curStrPos);\n\t      }\n\t      this.options.onStart(this.arrayPos, this);\n\t    }\n\t\n\t    /**\n\t     * Destroy this instance of Typed\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'destroy',\n\t    value: function destroy() {\n\t      this.reset(false);\n\t      this.options.onDestroy(this);\n\t    }\n\t\n\t    /**\n\t     * Reset Typed and optionally restarts\n\t     * @param {boolean} restart\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'reset',\n\t    value: function reset() {\n\t      var restart = arguments.length <= 0 || arguments[0] === undefined ? true : arguments[0];\n\t\n\t      clearInterval(this.timeout);\n\t      this.replaceText('');\n\t      if (this.cursor && this.cursor.parentNode) {\n\t        this.cursor.parentNode.removeChild(this.cursor);\n\t        this.cursor = null;\n\t      }\n\t      this.strPos = 0;\n\t      this.arrayPos = 0;\n\t      this.curLoop = 0;\n\t      if (restart) {\n\t        this.insertCursor();\n\t        this.options.onReset(this);\n\t        this.begin();\n\t      }\n\t    }\n\t\n\t    /**\n\t     * Begins the typing animation\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'begin',\n\t    value: function begin() {\n\t      var _this = this;\n\t\n\t      this.typingComplete = false;\n\t      this.shuffleStringsIfNeeded(this);\n\t      this.insertCursor();\n\t      if (this.bindInputFocusEvents) this.bindFocusEvents();\n\t      this.timeout = setTimeout(function () {\n\t        // Check if there is some text in the element, if yes start by backspacing the default message\n\t        if (!_this.currentElContent || _this.currentElContent.length === 0) {\n\t          _this.typewrite(_this.strings[_this.sequence[_this.arrayPos]], _this.strPos);\n\t        } else {\n\t          // Start typing\n\t          _this.backspace(_this.currentElContent, _this.currentElContent.length);\n\t        }\n\t      }, this.startDelay);\n\t    }\n\t\n\t    /**\n\t     * Called for each character typed\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'typewrite',\n\t    value: function typewrite(curString, curStrPos) {\n\t      var _this2 = this;\n\t\n\t      if (this.fadeOut && this.el.classList.contains(this.fadeOutClass)) {\n\t        this.el.classList.remove(this.fadeOutClass);\n\t        if (this.cursor) this.cursor.classList.remove(this.fadeOutClass);\n\t      }\n\t\n\t      var humanize = this.humanizer(this.typeSpeed);\n\t      var numChars = 1;\n\t\n\t      if (this.pause.status === true) {\n\t        this.setPauseStatus(curString, curStrPos, true);\n\t        return;\n\t      }\n\t\n\t      // contain typing function in a timeout humanize'd delay\n\t      this.timeout = setTimeout(function () {\n\t        // skip over any HTML chars\n\t        curStrPos = _htmlParserJs.htmlParser.typeHtmlChars(curString, curStrPos, _this2);\n\t\n\t        var pauseTime = 0;\n\t        var substr = curString.substr(curStrPos);\n\t        // check for an escape character before a pause value\n\t        // format: \\^\\d+ .. eg: ^1000 .. should be able to print the ^ too using ^^\n\t        // single ^ are removed from string\n\t        if (substr.charAt(0) === '^') {\n\t          if (/^\\^\\d+/.test(substr)) {\n\t            var skip = 1; // skip at least 1\n\t            substr = /\\d+/.exec(substr)[0];\n\t            skip += substr.length;\n\t            pauseTime = parseInt(substr);\n\t            _this2.temporaryPause = true;\n\t            _this2.options.onTypingPaused(_this2.arrayPos, _this2);\n\t            // strip out the escape character and pause value so they're not printed\n\t            curString = curString.substring(0, curStrPos) + curString.substring(curStrPos + skip);\n\t            _this2.toggleBlinking(true);\n\t          }\n\t        }\n\t\n\t        // check for skip characters formatted as\n\t        // \"this is a `string to print NOW` ...\"\n\t        if (substr.charAt(0) === '`') {\n\t          while (curString.substr(curStrPos + numChars).charAt(0) !== '`') {\n\t            numChars++;\n\t            if (curStrPos + numChars > curString.length) break;\n\t          }\n\t          // strip out the escape characters and append all the string in between\n\t          var stringBeforeSkip = curString.substring(0, curStrPos);\n\t          var stringSkipped = curString.substring(stringBeforeSkip.length + 1, curStrPos + numChars);\n\t          var stringAfterSkip = curString.substring(curStrPos + numChars + 1);\n\t          curString = stringBeforeSkip + stringSkipped + stringAfterSkip;\n\t          numChars--;\n\t        }\n\t\n\t        // timeout for any pause after a character\n\t        _this2.timeout = setTimeout(function () {\n\t          // Accounts for blinking while paused\n\t          _this2.toggleBlinking(false);\n\t\n\t          // We're done with this sentence!\n\t          if (curStrPos === curString.length) {\n\t            _this2.doneTyping(curString, curStrPos);\n\t          } else {\n\t            _this2.keepTyping(curString, curStrPos, numChars);\n\t          }\n\t          // end of character pause\n\t          if (_this2.temporaryPause) {\n\t            _this2.temporaryPause = false;\n\t            _this2.options.onTypingResumed(_this2.arrayPos, _this2);\n\t          }\n\t        }, pauseTime);\n\t\n\t        // humanized value for typing\n\t      }, humanize);\n\t    }\n\t\n\t    /**\n\t     * Continue to the next string & begin typing\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'keepTyping',\n\t    value: function keepTyping(curString, curStrPos, numChars) {\n\t      // call before functions if applicable\n\t      if (curStrPos === 0) {\n\t        this.toggleBlinking(false);\n\t        this.options.preStringTyped(this.arrayPos, this);\n\t      }\n\t      // start typing each new char into existing string\n\t      // curString: arg, this.el.html: original text inside element\n\t      curStrPos += numChars;\n\t      var nextString = curString.substr(0, curStrPos);\n\t      this.replaceText(nextString);\n\t      // loop the function\n\t      this.typewrite(curString, curStrPos);\n\t    }\n\t\n\t    /**\n\t     * We're done typing all strings\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'doneTyping',\n\t    value: function doneTyping(curString, curStrPos) {\n\t      var _this3 = this;\n\t\n\t      // fires callback function\n\t      this.options.onStringTyped(this.arrayPos, this);\n\t      this.toggleBlinking(true);\n\t      // is this the final string\n\t      if (this.arrayPos === this.strings.length - 1) {\n\t        // callback that occurs on the last typed string\n\t        this.complete();\n\t        // quit if we wont loop back\n\t        if (this.loop === false || this.curLoop === this.loopCount) {\n\t          return;\n\t        }\n\t      }\n\t      this.timeout = setTimeout(function () {\n\t        _this3.backspace(curString, curStrPos);\n\t      }, this.backDelay);\n\t    }\n\t\n\t    /**\n\t     * Backspaces 1 character at a time\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'backspace',\n\t    value: function backspace(curString, curStrPos) {\n\t      var _this4 = this;\n\t\n\t      if (this.pause.status === true) {\n\t        this.setPauseStatus(curString, curStrPos, true);\n\t        return;\n\t      }\n\t      if (this.fadeOut) return this.initFadeOut();\n\t\n\t      this.toggleBlinking(false);\n\t      var humanize = this.humanizer(this.backSpeed);\n\t\n\t      this.timeout = setTimeout(function () {\n\t        curStrPos = _htmlParserJs.htmlParser.backSpaceHtmlChars(curString, curStrPos, _this4);\n\t        // replace text with base text + typed characters\n\t        var curStringAtPosition = curString.substr(0, curStrPos);\n\t        _this4.replaceText(curStringAtPosition);\n\t\n\t        // if smartBack is enabled\n\t        if (_this4.smartBackspace) {\n\t          // the remaining part of the current string is equal of the same part of the new string\n\t          var nextString = _this4.strings[_this4.arrayPos + 1];\n\t          if (nextString && curStringAtPosition === nextString.substr(0, curStrPos)) {\n\t            _this4.stopNum = curStrPos;\n\t          } else {\n\t            _this4.stopNum = 0;\n\t          }\n\t        }\n\t\n\t        // if the number (id of character in current string) is\n\t        // less than the stop number, keep going\n\t        if (curStrPos > _this4.stopNum) {\n\t          // subtract characters one by one\n\t          curStrPos--;\n\t          // loop the function\n\t          _this4.backspace(curString, curStrPos);\n\t        } else if (curStrPos <= _this4.stopNum) {\n\t          // if the stop number has been reached, increase\n\t          // array position to next string\n\t          _this4.arrayPos++;\n\t          // When looping, begin at the beginning after backspace complete\n\t          if (_this4.arrayPos === _this4.strings.length) {\n\t            _this4.arrayPos = 0;\n\t            _this4.options.onLastStringBackspaced();\n\t            _this4.shuffleStringsIfNeeded();\n\t            _this4.begin();\n\t          } else {\n\t            _this4.typewrite(_this4.strings[_this4.sequence[_this4.arrayPos]], curStrPos);\n\t          }\n\t        }\n\t        // humanized value for typing\n\t      }, humanize);\n\t    }\n\t\n\t    /**\n\t     * Full animation is complete\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'complete',\n\t    value: function complete() {\n\t      this.options.onComplete(this);\n\t      if (this.loop) {\n\t        this.curLoop++;\n\t      } else {\n\t        this.typingComplete = true;\n\t      }\n\t    }\n\t\n\t    /**\n\t     * Has the typing been stopped\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @param {boolean} isTyping\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'setPauseStatus',\n\t    value: function setPauseStatus(curString, curStrPos, isTyping) {\n\t      this.pause.typewrite = isTyping;\n\t      this.pause.curString = curString;\n\t      this.pause.curStrPos = curStrPos;\n\t    }\n\t\n\t    /**\n\t     * Toggle the blinking cursor\n\t     * @param {boolean} isBlinking\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'toggleBlinking',\n\t    value: function toggleBlinking(isBlinking) {\n\t      if (!this.cursor) return;\n\t      // if in paused state, don't toggle blinking a 2nd time\n\t      if (this.pause.status) return;\n\t      if (this.cursorBlinking === isBlinking) return;\n\t      this.cursorBlinking = isBlinking;\n\t      var status = isBlinking ? 'infinite' : 0;\n\t      this.cursor.style.animationIterationCount = status;\n\t    }\n\t\n\t    /**\n\t     * Speed in MS to type\n\t     * @param {number} speed\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'humanizer',\n\t    value: function humanizer(speed) {\n\t      return Math.round(Math.random() * speed / 2) + speed;\n\t    }\n\t\n\t    /**\n\t     * Shuffle the sequence of the strings array\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'shuffleStringsIfNeeded',\n\t    value: function shuffleStringsIfNeeded() {\n\t      if (!this.shuffle) return;\n\t      this.sequence = this.sequence.sort(function () {\n\t        return Math.random() - 0.5;\n\t      });\n\t    }\n\t\n\t    /**\n\t     * Adds a CSS class to fade out current string\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'initFadeOut',\n\t    value: function initFadeOut() {\n\t      var _this5 = this;\n\t\n\t      this.el.className += ' ' + this.fadeOutClass;\n\t      if (this.cursor) this.cursor.className += ' ' + this.fadeOutClass;\n\t      return setTimeout(function () {\n\t        _this5.arrayPos++;\n\t        _this5.replaceText('');\n\t\n\t        // Resets current string if end of loop reached\n\t        if (_this5.strings.length > _this5.arrayPos) {\n\t          _this5.typewrite(_this5.strings[_this5.sequence[_this5.arrayPos]], 0);\n\t        } else {\n\t          _this5.typewrite(_this5.strings[0], 0);\n\t          _this5.arrayPos = 0;\n\t        }\n\t      }, this.fadeOutDelay);\n\t    }\n\t\n\t    /**\n\t     * Replaces current text in the HTML element\n\t     * depending on element type\n\t     * @param {string} str\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'replaceText',\n\t    value: function replaceText(str) {\n\t      if (this.attr) {\n\t        this.el.setAttribute(this.attr, str);\n\t      } else {\n\t        if (this.isInput) {\n\t          this.el.value = str;\n\t        } else if (this.contentType === 'html') {\n\t          this.el.innerHTML = str;\n\t        } else {\n\t          this.el.textContent = str;\n\t        }\n\t      }\n\t    }\n\t\n\t    /**\n\t     * If using input elements, bind focus in order to\n\t     * start and stop the animation\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'bindFocusEvents',\n\t    value: function bindFocusEvents() {\n\t      var _this6 = this;\n\t\n\t      if (!this.isInput) return;\n\t      this.el.addEventListener('focus', function (e) {\n\t        _this6.stop();\n\t      });\n\t      this.el.addEventListener('blur', function (e) {\n\t        if (_this6.el.value && _this6.el.value.length !== 0) {\n\t          return;\n\t        }\n\t        _this6.start();\n\t      });\n\t    }\n\t\n\t    /**\n\t     * On init, insert the cursor element\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'insertCursor',\n\t    value: function insertCursor() {\n\t      if (!this.showCursor) return;\n\t      if (this.cursor) return;\n\t      this.cursor = document.createElement('span');\n\t      this.cursor.className = 'typed-cursor';\n\t      this.cursor.innerHTML = this.cursorChar;\n\t      this.el.parentNode && this.el.parentNode.insertBefore(this.cursor, this.el.nextSibling);\n\t    }\n\t  }]);\n\t\n\t  return Typed;\n\t})();\n\t\n\texports['default'] = Typed;\n\tmodule.exports = exports['default'];\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\t\n\tvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\t\n\tvar _createClass = (function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; })();\n\t\n\tfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }\n\t\n\tfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }\n\t\n\tvar _defaultsJs = __webpack_require__(2);\n\t\n\tvar _defaultsJs2 = _interopRequireDefault(_defaultsJs);\n\t\n\t/**\n\t * Initialize the Typed object\n\t */\n\t\n\tvar Initializer = (function () {\n\t  function Initializer() {\n\t    _classCallCheck(this, Initializer);\n\t  }\n\t\n\t  _createClass(Initializer, [{\n\t    key: 'load',\n\t\n\t    /**\n\t     * Load up defaults & options on the Typed instance\n\t     * @param {Typed} self instance of Typed\n\t     * @param {object} options options object\n\t     * @param {string} elementId HTML element ID _OR_ instance of HTML element\n\t     * @private\n\t     */\n\t\n\t    value: function load(self, options, elementId) {\n\t      // chosen element to manipulate text\n\t      if (typeof elementId === 'string') {\n\t        self.el = document.querySelector(elementId);\n\t      } else {\n\t        self.el = elementId;\n\t      }\n\t\n\t      self.options = _extends({}, _defaultsJs2['default'], options);\n\t\n\t      // attribute to type into\n\t      self.isInput = self.el.tagName.toLowerCase() === 'input';\n\t      self.attr = self.options.attr;\n\t      self.bindInputFocusEvents = self.options.bindInputFocusEvents;\n\t\n\t      // show cursor\n\t      self.showCursor = self.isInput ? false : self.options.showCursor;\n\t\n\t      // custom cursor\n\t      self.cursorChar = self.options.cursorChar;\n\t\n\t      // Is the cursor blinking\n\t      self.cursorBlinking = true;\n\t\n\t      // text content of element\n\t      self.elContent = self.attr ? self.el.getAttribute(self.attr) : self.el.textContent;\n\t\n\t      // html or plain text\n\t      self.contentType = self.options.contentType;\n\t\n\t      // typing speed\n\t      self.typeSpeed = self.options.typeSpeed;\n\t\n\t      // add a delay before typing starts\n\t      self.startDelay = self.options.startDelay;\n\t\n\t      // backspacing speed\n\t      self.backSpeed = self.options.backSpeed;\n\t\n\t      // only backspace what doesn't match the previous string\n\t      self.smartBackspace = self.options.smartBackspace;\n\t\n\t      // amount of time to wait before backspacing\n\t      self.backDelay = self.options.backDelay;\n\t\n\t      // Fade out instead of backspace\n\t      self.fadeOut = self.options.fadeOut;\n\t      self.fadeOutClass = self.options.fadeOutClass;\n\t      self.fadeOutDelay = self.options.fadeOutDelay;\n\t\n\t      // variable to check whether typing is currently paused\n\t      self.isPaused = false;\n\t\n\t      // input strings of text\n\t      self.strings = self.options.strings.map(function (s) {\n\t        return s.trim();\n\t      });\n\t\n\t      // div containing strings\n\t      if (typeof self.options.stringsElement === 'string') {\n\t        self.stringsElement = document.querySelector(self.options.stringsElement);\n\t      } else {\n\t        self.stringsElement = self.options.stringsElement;\n\t      }\n\t\n\t      if (self.stringsElement) {\n\t        self.strings = [];\n\t        self.stringsElement.style.display = 'none';\n\t        var strings = Array.prototype.slice.apply(self.stringsElement.children);\n\t        var stringsLength = strings.length;\n\t\n\t        if (stringsLength) {\n\t          for (var i = 0; i < stringsLength; i += 1) {\n\t            var stringEl = strings[i];\n\t            self.strings.push(stringEl.innerHTML.trim());\n\t          }\n\t        }\n\t      }\n\t\n\t      // character number position of current string\n\t      self.strPos = 0;\n\t\n\t      // current array position\n\t      self.arrayPos = 0;\n\t\n\t      // index of string to stop backspacing on\n\t      self.stopNum = 0;\n\t\n\t      // Looping logic\n\t      self.loop = self.options.loop;\n\t      self.loopCount = self.options.loopCount;\n\t      self.curLoop = 0;\n\t\n\t      // shuffle the strings\n\t      self.shuffle = self.options.shuffle;\n\t      // the order of strings\n\t      self.sequence = [];\n\t\n\t      self.pause = {\n\t        status: false,\n\t        typewrite: true,\n\t        curString: '',\n\t        curStrPos: 0\n\t      };\n\t\n\t      // When the typing is complete (when not looped)\n\t      self.typingComplete = false;\n\t\n\t      // Set the order in which the strings are typed\n\t      for (var i in self.strings) {\n\t        self.sequence[i] = i;\n\t      }\n\t\n\t      // If there is some text in the element\n\t      self.currentElContent = this.getCurrentElContent(self);\n\t\n\t      self.autoInsertCss = self.options.autoInsertCss;\n\t\n\t      this.appendAnimationCss(self);\n\t    }\n\t  }, {\n\t    key: 'getCurrentElContent',\n\t    value: function getCurrentElContent(self) {\n\t      var elContent = '';\n\t      if (self.attr) {\n\t        elContent = self.el.getAttribute(self.attr);\n\t      } else if (self.isInput) {\n\t        elContent = self.el.value;\n\t      } else if (self.contentType === 'html') {\n\t        elContent = self.el.innerHTML;\n\t      } else {\n\t        elContent = self.el.textContent;\n\t      }\n\t      return elContent;\n\t    }\n\t  }, {\n\t    key: 'appendAnimationCss',\n\t    value: function appendAnimationCss(self) {\n\t      if (!self.autoInsertCss) {\n\t        return;\n\t      }\n\t      if (!self.showCursor || !self.fadeOut) {\n\t        return;\n\t      }\n\t\n\t      var css = document.createElement('style');\n\t      css.type = 'text/css';\n\t      var innerCss = '';\n\t      if (self.showCursor) {\n\t        innerCss += '\\n        .typed-cursor{\\n          opacity: 1;\\n          animation: typedjsBlink 0.7s infinite;\\n          -webkit-animation: typedjsBlink 0.7s infinite;\\n                  animation: typedjsBlink 0.7s infinite;\\n        }\\n        @keyframes typedjsBlink{\\n          50% { opacity: 0.0; }\\n        }\\n        @-webkit-keyframes typedjsBlink{\\n          0% { opacity: 1; }\\n          50% { opacity: 0.0; }\\n          100% { opacity: 1; }\\n        }\\n      ';\n\t      }\n\t      if (self.fadeOut) {\n\t        innerCss += '\\n        .typed-fade-out{\\n          opacity: 0;\\n          transition: opacity .25s;\\n          -webkit-animation: 0;\\n                  animation: 0;\\n        }\\n      ';\n\t      }\n\t      if (css.length === 0) {\n\t        return;\n\t      }\n\t      css.innerHTML = innerCss;\n\t      document.head.appendChild(css);\n\t    }\n\t  }]);\n\t\n\t  return Initializer;\n\t})();\n\t\n\texports['default'] = Initializer;\n\tvar initializer = new Initializer();\n\texports.initializer = initializer;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Defaults & options\n\t * @returns {object} Typed defaults & options\n\t * @public\n\t */\n\t\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\tvar defaults = {\n\t  /**\n\t   * @property {array} strings strings to be typed\n\t   * @property {string} stringsElement ID of element containing string children\n\t   */\n\t  strings: ['These are the default values...', 'You know what you should do?', 'Use your own!', 'Have a great day!'],\n\t  stringsElement: null,\n\t\n\t  /**\n\t   * @property {number} typeSpeed type speed in milliseconds\n\t   */\n\t  typeSpeed: 0,\n\t\n\t  /**\n\t   * @property {number} startDelay time before typing starts in milliseconds\n\t   */\n\t  startDelay: 0,\n\t\n\t  /**\n\t   * @property {number} backSpeed backspacing speed in milliseconds\n\t   */\n\t  backSpeed: 0,\n\t\n\t  /**\n\t   * @property {boolean} smartBackspace only backspace what doesn't match the previous string\n\t   */\n\t  smartBackspace: true,\n\t\n\t  /**\n\t   * @property {boolean} shuffle shuffle the strings\n\t   */\n\t  shuffle: false,\n\t\n\t  /**\n\t   * @property {number} backDelay time before backspacing in milliseconds\n\t   */\n\t  backDelay: 700,\n\t\n\t  /**\n\t   * @property {boolean} fadeOut Fade out instead of backspace\n\t   * @property {string} fadeOutClass css class for fade animation\n\t   * @property {boolean} fadeOutDelay Fade out delay in milliseconds\n\t   */\n\t  fadeOut: false,\n\t  fadeOutClass: 'typed-fade-out',\n\t  fadeOutDelay: 500,\n\t\n\t  /**\n\t   * @property {boolean} loop loop strings\n\t   * @property {number} loopCount amount of loops\n\t   */\n\t  loop: false,\n\t  loopCount: Infinity,\n\t\n\t  /**\n\t   * @property {boolean} showCursor show cursor\n\t   * @property {string} cursorChar character for cursor\n\t   * @property {boolean} autoInsertCss insert CSS for cursor and fadeOut into HTML <head>\n\t   */\n\t  showCursor: true,\n\t  cursorChar: '|',\n\t  autoInsertCss: true,\n\t\n\t  /**\n\t   * @property {string} attr attribute for typing\n\t   * Ex: input placeholder, value, or just HTML text\n\t   */\n\t  attr: null,\n\t\n\t  /**\n\t   * @property {boolean} bindInputFocusEvents bind to focus and blur if el is text input\n\t   */\n\t  bindInputFocusEvents: false,\n\t\n\t  /**\n\t   * @property {string} contentType 'html' or 'null' for plaintext\n\t   */\n\t  contentType: 'html',\n\t\n\t  /**\n\t   * All typing is complete\n\t   * @param {Typed} self\n\t   */\n\t  onComplete: function onComplete(self) {},\n\t\n\t  /**\n\t   * Before each string is typed\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  preStringTyped: function preStringTyped(arrayPos, self) {},\n\t\n\t  /**\n\t   * After each string is typed\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onStringTyped: function onStringTyped(arrayPos, self) {},\n\t\n\t  /**\n\t   * During looping, after last string is typed\n\t   * @param {Typed} self\n\t   */\n\t  onLastStringBackspaced: function onLastStringBackspaced(self) {},\n\t\n\t  /**\n\t   * Typing has been stopped\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onTypingPaused: function onTypingPaused(arrayPos, self) {},\n\t\n\t  /**\n\t   * Typing has been started after being stopped\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onTypingResumed: function onTypingResumed(arrayPos, self) {},\n\t\n\t  /**\n\t   * After reset\n\t   * @param {Typed} self\n\t   */\n\t  onReset: function onReset(self) {},\n\t\n\t  /**\n\t   * After stop\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onStop: function onStop(arrayPos, self) {},\n\t\n\t  /**\n\t   * After start\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onStart: function onStart(arrayPos, self) {},\n\t\n\t  /**\n\t   * After destroy\n\t   * @param {Typed} self\n\t   */\n\t  onDestroy: function onDestroy(self) {}\n\t};\n\t\n\texports['default'] = defaults;\n\tmodule.exports = exports['default'];\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * TODO: These methods can probably be combined somehow\n\t * Parse HTML tags & HTML Characters\n\t */\n\t\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\t\n\tvar _createClass = (function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; })();\n\t\n\tfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }\n\t\n\tvar HTMLParser = (function () {\n\t  function HTMLParser() {\n\t    _classCallCheck(this, HTMLParser);\n\t  }\n\t\n\t  _createClass(HTMLParser, [{\n\t    key: 'typeHtmlChars',\n\t\n\t    /**\n\t     * Type HTML tags & HTML Characters\n\t     * @param {string} curString Current string\n\t     * @param {number} curStrPos Position in current string\n\t     * @param {Typed} self instance of Typed\n\t     * @returns {number} a new string position\n\t     * @private\n\t     */\n\t\n\t    value: function typeHtmlChars(curString, curStrPos, self) {\n\t      if (self.contentType !== 'html') return curStrPos;\n\t      var curChar = curString.substr(curStrPos).charAt(0);\n\t      if (curChar === '<' || curChar === '&') {\n\t        var endTag = '';\n\t        if (curChar === '<') {\n\t          endTag = '>';\n\t        } else {\n\t          endTag = ';';\n\t        }\n\t        while (curString.substr(curStrPos + 1).charAt(0) !== endTag) {\n\t          curStrPos++;\n\t          if (curStrPos + 1 > curString.length) {\n\t            break;\n\t          }\n\t        }\n\t        curStrPos++;\n\t      }\n\t      return curStrPos;\n\t    }\n\t\n\t    /**\n\t     * Backspace HTML tags and HTML Characters\n\t     * @param {string} curString Current string\n\t     * @param {number} curStrPos Position in current string\n\t     * @param {Typed} self instance of Typed\n\t     * @returns {number} a new string position\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'backSpaceHtmlChars',\n\t    value: function backSpaceHtmlChars(curString, curStrPos, self) {\n\t      if (self.contentType !== 'html') return curStrPos;\n\t      var curChar = curString.substr(curStrPos).charAt(0);\n\t      if (curChar === '>' || curChar === ';') {\n\t        var endTag = '';\n\t        if (curChar === '>') {\n\t          endTag = '<';\n\t        } else {\n\t          endTag = '&';\n\t        }\n\t        while (curString.substr(curStrPos - 1).charAt(0) !== endTag) {\n\t          curStrPos--;\n\t          if (curStrPos < 0) {\n\t            break;\n\t          }\n\t        }\n\t        curStrPos--;\n\t      }\n\t      return curStrPos;\n\t    }\n\t  }]);\n\t\n\t  return HTMLParser;\n\t})();\n\t\n\texports['default'] = HTMLParser;\n\tvar htmlParser = new HTMLParser();\n\texports.htmlParser = htmlParser;\n\n/***/ })\n/******/ ])\n});\n;"]}